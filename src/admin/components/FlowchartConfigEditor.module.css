/* FlowchartConfigEditor Styles */

.container {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 16px;
  background-color: #f9fafb;
}

.header {
  margin-bottom: 16px;
}

.title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 8px;
}

.instructions {
  margin-bottom: 12px;
  padding: 12px;
  background-color: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
  font-size: 14px;
  color: #1e40af;
}

.instructionsTitle {
  font-weight: 600;
}

.instructionsList {
  margin-top: 4px;
  margin-left: 16px;
  list-style-type: disc;
}

.buttonContainer {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.addButton {
  padding: 8px 12px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.addButton:hover {
  background-color: #2563eb;
}

.addInnovationButton {
  padding: 8px 12px;
  background-color: #a855f7;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.addInnovationButton:hover {
  background-color: #9333ea;
}

.clearButton {
  padding: 8px 12px;
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.clearButton:hover {
  background-color: #dc2626;
}

.flowContainer {
  height: 500px;
  width: 100%;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  background-color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.stats {
  margin-top: 12px;
  font-size: 12px;
  color: #6b7280;
}

/* Node Styles - Modern design inspired by FlowChart.tsx */
.nodeContainer {
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  background-color: white;
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  min-width: 140px;
  max-width: 280px;
  position: relative;
  transition: all 0.3s ease;
  overflow: visible;
}

.nodeContainer:hover {
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.nodeContainer:hover .deleteButton {
  opacity: 1;
}

.nodeContainer:hover .gradientBorder {
  opacity: 1;
}

/* Gradient border effect */
.gradientBorder {
  position: absolute;
  inset: 0;
  border-radius: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  background: linear-gradient(to right, #6366f1, #8b5cf6, #d946ef);
  padding: 1px;
  margin: -1px;
  z-index: -1;
}

.deleteButton {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 12px;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nodeInput {
  width: 100%;
  padding: 8px;
  border: 1px solid #93c5fd;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
}

.nodeInput:focus {
  border-color: #3b82f6;
}

.nodeLabel {
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  font-size: 18px;
  font-weight: 600;
  transition: background-color 0.2s;
  color: #1f2937;
  margin-bottom: 4px;
  text-align: center;
}

.nodeLabel:hover {
  background-color: #f3f4f6;
}

.nodeHandle {
  width: 12px;
  height: 12px;
  background-color: #6366f1;
  border: 2px solid white;
  border-radius: 50%;
  position: absolute;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nodeHandle:hover {
  background-color: #4f46e5;
  transform: scale(1.3);
  z-index: 10;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Edge Styles - Modern design */
.edgePath {
  stroke: #6366f1;
  stroke-width: 2;
  fill: none;
  marker-end: url(#arrowhead);
  transition: all 0.2s ease;
}

.edgePath:hover {
  stroke-width: 3;
  stroke: #4f46e5;
}

.innovationEdgePath {
  stroke: #a855f7;
  stroke-width: 3;
  fill: none;
  marker-end: url(#innovationArrowhead);
  stroke-dasharray: 8, 4;
  transition: all 0.2s ease;
}

.innovationEdgePath:hover {
  stroke-width: 4;
  stroke: #9333ea;
}

.edgeLabel {
  padding: 6px 12px;
  font-size: 12px;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  text-align: center;
  transition: all 0.2s ease;
  color: #374151;
  z-index: 10;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.edgeLabel:hover {
  background-color: #f9fafb;
  border-color: #6366f1;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.edgeLabel:active {
  cursor: grabbing !important;
  transform: translateY(0px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

.innovationEdgeLabel {
  padding: 6px 12px;
  font-size: 12px;
  background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
  border: 2px solid #a855f7;
  border-radius: 8px;
  cursor: pointer;
  text-align: center;
  transition: all 0.2s ease;
  font-weight: 600;
  color: #7c3aed;
  box-shadow: 0 2px 4px rgba(168, 85, 247, 0.2);
}

.innovationEdgeLabel:hover {
  background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(168, 85, 247, 0.3);
}

.innovationEdgeLabel:active {
  cursor: grabbing !important;
  transform: translateY(0px);
  box-shadow: 0 6px 12px rgba(168, 85, 247, 0.4);
}

.edgeInput {
  width: 100%;
  padding: 2px 4px;
  font-size: 12px;
  border: 1px solid #93c5fd;
  border-radius: 4px;
  outline: none;
}

.edgeInputContainer {
  overflow: visible;
}

.edgeDeleteButton {
  width: 18px;
  height: 18px;
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  z-index: 100;
}

.edgeDeleteButton:hover {
  background-color: #dc2626;
}

.edgeInsertButton {
  width: 18px;
  height: 18px;
  background-color: #10b981;
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  font-weight: bold;
}

.edgeInsertButton:hover {
  background-color: #059669;
}

/* Controls */
.controls {
  background-color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Background - ReactFlow Background component doesn't use className for color */

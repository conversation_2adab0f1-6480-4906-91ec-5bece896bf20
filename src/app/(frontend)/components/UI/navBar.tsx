import Image from 'next/image';
import Link from 'next/link';
import { Suspense } from 'react'; // Added useRef
import { ROUTES } from '../../constants/routes';
import { HeaderProps } from '../../types/data.types';
import AuthButton from '../Buttons/buttons';
interface MenuItemsType {
  href: string; // Added type annotation
  label: string; // Added type annotation
}
const menuItems = [
  { href: ROUTES.HOME, label: 'Home' },
  { href: ROUTES.BROWSE_INNOVATIONS, label: 'Browse Innovations' },
  { href: ROUTES.MY_INNOVATIONS, label: 'My Innovations' },
  { href: ROUTES.ABOUT, label: 'About Us' },
  { href: ROUTES.HOW_TO_SUBMIT_INNOVATION, label: 'Submit an Innovation' },
  { href: ROUTES.FLOWCHART_DEMO, label: 'Flowchart Demo' },
] as MenuItemsType[]; // Added type annotation
export const NavBarUI: React.FC<HeaderProps> = ({
  color,
  isWhiteBg,
  isAuthenticated,
  pathname,
}) => {
  const linkClass = (path: string) =>
    `relative text-sm sm:text-base font-medium after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-0 after:h-[2px]
     ${color ? 'after:bg-white border-Pure-White-FFFFFF' : 'after:bg-blue-900 border-blue-900'} after:transition-all after:duration-300 hover:after:w-full
     ${pathname === path ? 'font-semibold text-Midnight-Royal-Blue-02018B border-b-2' : 'text-Midnight-Royal-Blue-02018B'}
     font-Poppins`;

  const gradientEffect = isWhiteBg
    ? 'bg-white'
    : 'backdrop-blur-lg bg-gradient-to-r from-[#0F06D2]/90 via-[#85367A]/90 to-[#F86525]/90 bg-opacity-50';
  return (
    <nav id="navbar" className={`fixed top-0 left-0 right-0 z-500 px-4 ${gradientEffect}`}>
      <div className="py-3 flex justify-between items-center max-w-[97vw] mx-auto">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-2">
          <Image
            alt="Logo"
            className="h-8 w-8 sm:h-10 sm:w-10"
            height={40}
            width={40}
            src="/images/icons/Atlas-of-Innovators.png"
          />
          <span
            className={`text-lg sm:text-xl md:text-2xl font-bold ${color || 'text-Deep-Royal-Blue-07038D'} font-Poppins`}
          >
            Atlas of Innovations
          </span>
        </Link>

        {/* Desktop Navigation */}
        <div
          className={`hidden lg:flex items-center space-x-4 xl:space-x-6 p-3 rounded-3xl  bg-Pure-White-FFFFFF/10 border-2 border-Pure-White-FFFFFF/20 backdrop-blur-lg hover:bg-Pure-White-FFFFFF/20 focus:ring-Pure-White-FFFFFF/30 ${color ? 'bg-Pure-White-FFFFFF/10' : 'bg-gray-50 shadow-sm'}`}
        >
          {menuItems
            .filter((v) => v.label !== 'My Innovations')
            .map(({ href, label }) => (
              <Link
                key={href + label}
                href={href}
                className={linkClass(href)}
                id={'nav-link-' + href}
              >
                <span className={`${color ? color : ''} relative inline-block whitespace-nowrap`}>
                  <span
                    aria-hidden="true"
                    className={`${color ? color : ''} whitespace-nowrap invisible absolute`}
                  >
                    {label}
                  </span>
                  {label}
                </span>
              </Link>
            ))}
        </div>

        {/* Auth Button */}
        <div className="">
          <Suspense fallback={<div>Loading...</div>}>
            <AuthButton isAuthenticated={isAuthenticated} color={color} menuItems={menuItems} />
          </Suspense>
        </div>
      </div>
    </nav>
  );
};
